/* eslint-disable import/no-cycle */
import React, {
  useCallback,
  useEffect,
  useState,
  useContext,
  useRef,
  memo,
} from 'react';
import lodash, { isObject } from 'lodash';
import PropType, { func, string } from 'prop-types';
import { connect } from 'react-redux';
import { useForm } from 'react-hook-form';
import { bindActionCreators } from 'redux';
import moment from 'moment';
import { useTranslation } from 'react-i18next';

import {
  AlertDialogFunction,
  Paragraph,
  PageDialog,
  PageDialogFooter,
  PageDialogTitle,
  PageDialogContent,
  Button,
  Paper,
  Flex,
  ModalDialog,
  ToastContext,
  ModalDialogTitle,
  ModalDialogContent,
  ModalDialogFooter,
  IconButton,
  Separator,
  Box,
} from '@majoo-ui/react';
import { TrashOutline, EllipsisHorizontalOutline } from '@majoo-ui/icons';
import { foundations } from '@majoo-ui/core';
import { useParams, useLocation } from 'react-router-dom';

import { useWhitelistCheck } from '~/hooks/useWhitelistCheck';

import * as outletActions from '../../../../data/outlets/actions';

import * as productApi from '../../../../data/product';
import * as inventoryApi from '../../../../data/inventories';
import * as outletApi from '../../../../data/outlets';
import { printExcel } from '../../../../utils/printout';
import { catchError, ishasProperty } from '../../../../utils/helper';
import userUtil from '../../../../utils/user.util';

import CoreHOC from '../../../../core/CoreHOC';
import FormStockOpname from './StockOpnameForm';
import InputCheckbox from '../../../../components/form/InputCheckboxDark';
import usePrevious from '../../../../utils/usePrevious';
import { useMediaQuery } from '../../../../utils/useMediaQuery';
import StockOpnameImportBannerStatus from './StockOpnameImportBannerStatus';
import SerialBatchNumberDialog from '../../components/SNBNDialog';
import {
  draftTagChecker,
  itemSNBNHaventEntry,
  itemSNBNQtyValidation,
  compareChangedDetected,
  reformatDefaultSOProducts,
} from './StockOpnameV2Utils';
import { matchMediaChecker, MATCH_MEDIA_TYPE } from '../../components/SNBNDialog/utils';
import InfoDialog from '../../components/InfoDialog';
import { INVENTORIES_STATUS } from '../../../../data/inventories/enum';
import { accountType } from '../../../../config/enum';
import { getInfoStorage } from '../../../../components/onlineorder/utils/storage';
import StockStatusPaper from './StockStatusPaper';

export const SORT_KEY = 'sort:stock_opname';

export const StockOpnameContext = React.createContext();

const StockOpnameCreate = memo(({
  userWhitelist, actions, menuPrivilege, ...props
}) => {
  const { t, i18n, ready } = useTranslation(['Penjualan/Inventori/stokOpname', 'Penjualan/Inventori/purchaseInvoice', 'translation']);
  const snbnDialogRef = useRef(null);

  const { location, params } = props;

  const { type } = params;
  const { state: stateRouter } = location;
  const isDetail = type === 'detail';

  const { addToast } = useContext(ToastContext);
  const {
    router, showProgress, hideProgress, filterBranch, merchantId,
  } = props;
  const { isEligible: isBackdateWhitelisted, schemes, checkEligibility } = useWhitelistCheck(merchantId);
  const [modalOption, toggleModalOption] = React.useState(false);
  const isMobile = useMediaQuery('(max-width: 767px)');
  const [statusSO, setStatusSO] = useState('');
  const [statusSOMessage, setStatusSOMessage] = useState('');
  const [listOutlet, setListOutlet] = useState([]);
  const [selectedCabangInfo, setSelectedCabangInfo] = useState({});
  const [formData, setFormData] = useState({});
  const [detailSO, setDetailSO] = useState([]);
  const [isSystemStock, setisSystemStock] = useState(false);
  const [modalCetak, setModalCetak] = useState(false);
  const [bannerStatus, setBannerStatus] = useState(true);
  const [snbnTagDraftPopup, setSnbnTagDraftPopup] = useState(false);
  const [snbnFlag, setSnbnFlag] = useState('');
  const [checkedItems, setCheckedItems] = useState({});
  const [newSOData, setNewSOData] = useState([]);
  const [isShowAveragePrice, setIsShowAveragePrice] = useState(true);

  const [caseType, setCaseType] = useState('plus');
  const [dialogSubmitFlag, setDialogSubmitFlag] = useState(false);
  const [tableFetching, setTableFetching] = useState(false);
  const [soDetailActualErrors, setSoDetailActualErrors] = useState({});

  const selectedCabangInfoRef = useRef(selectedCabangInfo);

  const currentAccountType = userUtil.getLocalConfigByKey('accountType');
  const isPrimeAccount = [accountType.ENTERPRISE, accountType.PRIME, accountType.PRIMEPLUS].includes(currentAccountType);

  const {
    register, handleSubmit, getValues, setValue, watch,
  } = useForm({
    defaultValues: {
      outlet: {
        value: '',
        name: '',
      },
      nomorSo: '',
      selectedItem: [],
      catatan: '',
    },
  });
  
  const {
    control: controllerItems, register: registerItems,
    getValues: getValItems, setValue: setValItems,
    formState: { errors }, setError, clearErrors,
    watch: watchItems,
  } = useForm({
    defaultValues: {
      data: detailSO,
    },
  });

  const prevProps = usePrevious(type);
  const { data: detailSOData } = watchItems();

  const onClose = () => {
    router.push('/inventory/kelola-stok/stock-opname');
  };

  const deleteHandler = async (state, callback) => {
    const { M_Cabang_id_cabang, id_stock_opname } = state;
    const payload = { so_id: id_stock_opname, id_outlet: M_Cabang_id_cabang };

    try {
      showProgress();
      const { error, message } = await inventoryApi.deleteStockOpnameV1(payload);
      if (error === null) {
        addToast({
          title: t('mainPage.toast.successTitle', 'Berhasil'),
          description: (
            <div dangerouslySetInnerHTML={{ __html: t('mainPage.toast.delete.desc', { nomor: state.nomor }) }} />
          ),
          variant: 'success',
          dismissAfter: 2000,
        });
        onClose();
      } else if (!message) {
        router.push('/auth/login');
      } else {
        throw message;
      }
    } catch (e) {
      callback();
      addToast({
        title: t('mainPage.toast.failedTitle', 'Terjadi Kesalahan'),
        description: catchError(e),
        variant: 'failed',
        dismissAfter: 2000,
      });
    } finally {
      hideProgress();
    }
  };

  const onChangeStokActual = setNewSOData;

  const fetchOutletData = async () => {
    const fetchOutlet = outletApi.getOutletV3();
    const outletList = await fetchOutlet;
    return outletList.data;
  };

  const getCurrentCabang = useCallback(
    async (data) => {
      const outlet = await fetchOutletData();
      if (outlet) {
        const { M_Cabang_id_cabang } = data;
        const cabang = outlet.find(i => i.id_cabang === M_Cabang_id_cabang);
        const { id_cabang: value = '', cabang_usaha_name: name = '' } = cabang;
        setValue('outlet', { value, name });
        selectedCabangInfoRef.current = cabang;
        setSelectedCabangInfo(cabang);
      }
    },
    [fetchOutletData, formData],
  );

  const buildSavePayload = (customData = false, data) => {
    let arrayListItems = [];
    const filterFormatter = item => ({
      id: item.M_Item_id_item,
      sku: item.M_Item_SKU,
      name: item.M_Item_name,
      so_item_qty_stock: item.stock_opname_detail_registered,
      item_stock_editable: item.stock_opname_detail_actual,
      catatan: item.stock_opname_note,
      disabled: false,
      item_bahan_jadi: item.item_bahan_jadi,
      isBahanJadi: item.item_bahan_jadi === '1',
      selisih: item.selisih,
      so_item_no: item.so_item_no,
      so_unit_no: item.so_unit_no,
      ...(isPrimeAccount && item.is_serial_number ? {
        is_serial_number: item.is_serial_number,
        serial_number: item.selisih < 0 ? { is_reserved: item.serial_number.is_reserved, serial_no: item.serial_number.serial_no.map(serialItem => (isObject(serialItem) ? serialItem.label : serialItem)) } : item.serial_number,
      } : {}),
      ...(isPrimeAccount && item.batch_number && item.batch_number.use_batch ? {
        is_batch_number: item.batch_number.use_batch,
        batch_number: {
          is_reserved: item.batch_number.batch_detail.length ? item.batch_number.is_reserved === undefined ? item.batch_number.is_draft : item.batch_number.is_reserved : false,
          use_batch: Boolean(item.batch_number.use_batch),
          batch_detail: item.batch_number.batch_detail.map(bnDetail => ({
            ...bnDetail,
            stock: bnDetail.stock < 0 ? Math.abs(bnDetail.stock) : bnDetail.stock,
          })),
          use_expired: item.batch_number.use_expired,
        },
      } : {}),
    });

    if (customData) {
      arrayListItems = data.map(filterFormatter);
    } else {
      arrayListItems = detailSOData.length > 0 ? detailSOData.map(filterFormatter) : [];
    }

    return arrayListItems;
  };

  const draftSave = async (data, isDraft = true) => {
    const detail = data.map((x) => {
      const retval = {
        so_item_id: Number(x.id),
        sku: x.sku,
        name: x.name,
        so_item_qty_stock: Number(x.so_item_qty_stock),
        so_item_qty_actual: Number(x.item_stock_editable),
        so_item_note: x.catatan,
        disabled: x.disabled,
        isBahanJadi: Boolean(x.item_bahan_jadi),
        selisih: x.selisih,
        ...(isPrimeAccount ? { is_serial_number: x.is_serial_number } : {}),
        ...(isPrimeAccount && x.is_serial_number ? {
          serial_number: x.serial_number,
        } : {}),
        ...(isPrimeAccount && x.is_batch_number ? {
          batch_number: {
            is_reserved: x.batch_number.batch_detail.length ? x.batch_number.is_reserved === undefined ? x.batch_number.is_draft : x.batch_number.is_reserved : false,
            use_batch: Boolean(x.batch_number.use_batch),
            batch_detail: x.batch_number.batch_detail.map(item => ({
              ...item,
              stock: item.stock < 0 ? Math.abs(item.stock) : item.stock,
            })),
            use_expired: x.batch_number.use_expired,
          },
        } : {}),
      };

      if (ishasProperty(x, 'item_no')) {
        Object.assign(retval, {
          so_item_no: x.item_no,
        });
      }

      if (ishasProperty(x, 'unit_no')) {
        Object.assign(retval, {
          so_unit_no: x.unit_no,
        });
      }

      if (ishasProperty(x, 'so_item_no')) {
        Object.assign(retval, {
          so_item_no: x.so_item_no,
        });
      }

      if (ishasProperty(x, 'so_unit_no')) {
        Object.assign(retval, {
          so_unit_no: x.so_unit_no,
        });
      }

      return retval;
    });
    const payload = {
      so_nomor: getValues('nomorSo'),
      id_outlet:
        params.type === 'create'
          ? Number(getValues('outlet.value'))
          // NOTE: issue was when create stock opname without refresh it send id_outlet from selectedCabangInfo.id_cabang value
          // but when has refresh it send empty object even had settled on mount, then fixing are set value of init currentCabang
          // into variable which use useRef hook
          : Number(selectedCabangInfo.id_cabang || selectedCabangInfoRef.current.id_cabang),
      so_catatan: getValues('catatan'),
      so_status: isDraft ? '2' : '1',
      so_items: detail,
      so_terbuang: 0,
      ...isBackdateWhitelisted && getValues('tanggal') && { tanggal: getValues('tanggal') }
    };

    let res = null;
    if (params.type === 'create') {
      res = await inventoryApi.createStockOpnameV2(payload);
    } else {
      Object.assign(payload, {
        so_id: params.id,
      });
      res = await inventoryApi.updateStockOpnameV2(payload);
    }
    return res;
  };

  const showPrintPopup = async () => {
    const { nomor, M_Cabang_id_cabang, id_stock_opname: soID } = formData;
    if (params.type !== 'create' && String(statusSO) === '1') {
      const itemId = detailSOData.map(x => x.id_stock_opname_detail);
      const payload = {
        // list_id_produk: JSON.stringify(itemId),
        id: params.id,
      };
      try {
        showProgress();
        const response = await inventoryApi.downloadPdfSov2(payload);
        if (response.data) {
          hideProgress();
          window.open(response.data, '_blank', `width=${window.screen.availWidth}, height=${window.screen.availHeight}`);
        } else {
          throw new Error(response.error);
        }
      } catch (error) {
        hideProgress();
        addToast({
          title: t('createEditModal.toast.print.errorTitle', 'Gagal mencetak stok opname'),
          description: catchError(error),
          variant: 'failed',
          dismissAfter: 2000,
        });
      }
    } else {
      setisSystemStock(false);
      setModalCetak(true);
    }
  };

  const doPrintHandler = async (callback) => {
    const { nomor, M_Cabang_id_cabang, id_stock_opname } = formData;
    const payload = {
      id: params.id,
      is_stok_sistem_checked: isSystemStock ? 1 : 0,
    };
    try {
      showProgress();
      const response = await inventoryApi.downloadPdfSov2(payload);
      if (response.data) {
        hideProgress();
        window.open(response.data, '_blank', `width=${window.screen.availWidth}, height=${window.screen.availHeight}`);
      } else {
        throw new Error(response.error);
      }
    } catch (error) {
      hideProgress();
      addToast({
        title: t('createEditModal.toast.print.errorTitle', 'Gagal mencetak stok opname'),
        description: catchError(error),
        variant: 'failed',
        dismissAfter: 2000,
      });
      callback();
    } finally {
      setModalCetak(false);
    }
  };

  const downloadTemplate = async () => {
    const { nomor, stock_opname_catatan } = formData;
    const { namaUsaha, logo } = props;

    if (detailSOData.length > 0) {
      const outlet = listOutlet.find(x => x.id === props.idCabang);
      const dashNumber = nomor.replace(/[&/\\#, +()$~%.'":*?<>{}]/g, '_');
      const template = 'TemplateStockOpname.xlsx';
      const outputName = dashNumber;
      const alias = 'x';

      /* api payload variable for excel header */
      const variable = {
        dateNow: moment().format('dd MMM yyyy'),
        usaha: namaUsaha,
        logo: !logo ? 'User.png' : logo,
        nomor,
        nama_outlet: outlet.name,
        stock_opname_catatan,
      };

      /* api payload data for datalist */
      const data = [];
      detailSOData.map(x => data.push({
        id_stock_opname_detail: x.id_stock_opname_detail,
        Stock_opname_id_stock_opname: x.Stock_opname_id_stock_opname,
        M_Item_id_item: x.M_Item_id_item,
        stock_opname_detail_actual: x.stock_opname_detail_actual,
        stock_opname_note: x.stock_opname_note,
        M_Item_SKU: x.M_Item_SKU,
        M_Item_name: x.M_Item_name,
        item_bahan_jadi: x.item_bahan_jadi,
        stock_opname_detail_registered: x.stock_opname_detail_registered,
        id_category: x.id_category,
      }));

      const kirim = [
        {
          variable,
          template,
          output_name: outputName,
          data,
          alias,
          usaha: '',
          user: '',
          extension: 'xlsx',
        },
      ];
      showProgress();
      printExcel(kirim, addToast);
      hideProgress();
    } else {
      addToast({
        title: t('createEditModal.toast.eksport.errorTitle', 'Ekspor Template Data Gagal'),
        description: t('createEditModal.toast.eksport.setGoodDesc', 'Atur Barang terlebih dahulu'),
        variant: 'failed',
        dismissAfter: 2000,
      });
    }
  };

  const dialogEkspor = () => {
    const dialog = new AlertDialogFunction({
      title: t('mainPage.confirmation.eksport.title', 'Ekspor Data Produk'),
      description: (
        <div dangerouslySetInnerHTML={{ __html: t('mainPage.confirmation.eksport.desc', { nomor: formData.nomor }) }} />
      ),
      labelCancel: t('mainPage.confirmation.btnAction.labelCancel', 'Batal'),
      labelConfirm: t('mainPage.confirmation.btnAction.labelConfirm', 'Ya, Lanjutkan'),
      dialogType: 'primary',
      onConfirm: downloadTemplate,
    });
    dialog.show();
  };

  const onDeleteAction = () => {
    if (params.type === 'detail') {
      const dialog = new AlertDialogFunction({
        title: t('mainPage.confirmation.delete.title', 'Hapus Stok Opname'),
        description: (
          <div dangerouslySetInnerHTML={{ __html: t('mainPage.confirmation.delete.desc', { nomor: formData.nomor }) }} />
        ),
        isMobile,
        labelConfirm: t('mainPage.confirmation.btnAction.labelConfirm', 'Ya, Lanjutkan'),
        labelCancel: t('mainPage.confirmation.btnAction.labelCancel', 'Batal'),
        dialogType: 'negative',
        onConfirm: () => deleteHandler(formData, onClose),
      });
      dialog.show();
    }
  };

  const onRemoveBarang = async (product) => {
    const remainingProducts = getValItems('data').filter(val => String(val.M_Item_id_item) !== String(product.M_Item_id_item));
    setValItems('data', remainingProducts);
    setDetailSO(remainingProducts);
    setNewSOData(remainingProducts);
  };

  const onBatalAction = () => {
    const toast = { dismissAfter: 2000 };
    let titleDialog = '';
    if (type === 'detail') {
      titleDialog = t('mainPage.confirmation.cancel.titleUpdate', 'Batal Ubah Data');
    } else {
      titleDialog = t('mainPage.confirmation.cancel.titleAdd', 'Batal Tambah Data');
    }
    const dialog = new AlertDialogFunction({
      title: titleDialog,
      isMobile,
      description: t('mainPage.confirmation.cancel.desc'),
      labelConfirm: t('createEditModal.footerSaveDraft', 'Simpan Draf'),
      labelCancel: t('mainPage.confirmation.btnAction.labelConfirm', 'Ya, Lanjutkan'),
      dialogType: 'primary',
      onCancel: onClose,
      onConfirm: async () => {
        showProgress();
        const { error, data, message } = await draftSave(buildSavePayload());
        if (error === null) {
          hideProgress();
          router.push(`/inventory/kelola-stok/stock-opname/detail/${data.id_stock_opname}`);
        } else {
          hideProgress();
          Object.assign(toast, {
            title: 'Gagal',
            description: message,
            variant: 'failed',
            values: {
              menu: t('mainPage.title', 'Stock Opname'),
            },
          });
          throw message;
        }
      },
    });
    dialog.show();
  };

  const handleCloseDetailDialog = () => {
    if (
      params.type === 'create'
      || (stateRouter && stateRouter.statusSO && stateRouter.statusSO === '2')
      || (stateRouter && stateRouter === '2')
      || (params.type === 'detail' && statusSO !== '' && statusSO === '2')
    ) {
      return onBatalAction();
    }
    return onClose();
  };

  const renderSaveButton = () => (type === 'create' ? menuPrivilege.isCanCreate : menuPrivilege.isCanUpdate);

  const onCompleteSave = (isComplete) => {
    const toast = { dismissAfter: 2000 };
    const isDraftDialog = {
      isMobile,
      title: t('mainPage.confirmation.draftSave.title', 'Simpan Draf Stok Opname'),
      description: (
        <div dangerouslySetInnerHTML={{ __html: t('mainPage.confirmation.draftSave.desc', { nomor: getValues('nomorSo') }) }} />
      ),
    };
    const dialogInfo = isComplete
      ? {
        title: t('mainPage.confirmation.save.title', 'Simpan Stok Opname'),
        isMobile,
        description: (
          <div dangerouslySetInnerHTML={{ __html: t('mainPage.confirmation.save.desc', { nomor: getValues('nomorSo') }) }} />
        ),
      }
      : isDraftDialog;

    if (
      (getValues('outlet.value') !== '' && detailSOData.length > 0)
      || (params.type === 'detail' && detailSOData.length > 0)
    ) {
      const dialog = new AlertDialogFunction({
        ...dialogInfo,
        labelConfirm: t('mainPage.confirmation.btnAction.labelConfirm', 'Ya, Lanjutkan'),
        labelCancel: t('mainPage.confirmation.btnAction.labelCancel', 'Batal'),
        dialogType: 'primary',
        onConfirm: async () => {
          showProgress();
          try {
            const { error, message } = await draftSave(
              buildSavePayload(true, getValItems('data')),
              !isComplete,
            );
            if (error === null) {
              onClose();
              addToast({
                ...toast,
                title: t('mainPage.toast.successTitle', 'Berhasil'),
                description: isComplete
                  ? (<div dangerouslySetInnerHTML={{ __html: t('mainPage.toast.save.desc', { nomor: getValues('nomorSo') }) }} />)
                  : (<div dangerouslySetInnerHTML={{ __html: t('mainPage.toast.draft.desc', { nomor: getValues('nomorSo') }) }} />),
                variant: 'success',
              });
            } else {
              onClose();
              addToast({
                ...toast,
                title: t('mainPage.toast.failTitle', 'Gagal'),
                description: message,
                variant: 'failed',
              });
              throw message;
            }
          } catch (error) {
            addToast({
              ...toast,
              title: t('mainPage.toast.failTitle', 'Gagal'),
              description: catchError(error),
              variant: 'failed',
              values: {
                menu: t('mainPage.title', 'Stock Opname'),
              },
            });
          } finally {
            hideProgress();
          }
        },
      });
      dialog.show();
    } else if (getValues('outlet.value') === '') {
      Object.assign(toast, {
        title: t('mainPage.toast.failTitle', 'Gagal'),
        description: t('mainPage.toast.setoutlet.desc', 'Silahkan pilih outlet terlebih dahulu'),
        variant: 'failed',
      });
    } else {
      Object.assign(toast, {
        title: t('mainPage.toast.failTitle', 'Gagal'),
        description: t('mainPage.toast.setgoods.desc', 'Silahkan atur barang terlebih dahulu'),
        variant: 'failed',
      });
    }
    if ('title' in toast) {
      addToast(toast);
    }
  };

  /**
   * NOTE: checkedItemFromDialog are passing checked value from checkbox on
   * TambahProdukModal global component, used for persisting item on detail,
   * stock opname data with item checked in TambahProdukModal, which is used
   * for stock opname transaction payload
   */
  const applyProduct = ({ addedItems, checkedItemFromDialog }, callback) => {
    let filteredProducts = addedItems.filter(product => product !== undefined);
    let existingProducts = [];

    if (detailSOData.length > 0) {
      filteredProducts = filteredProducts.filter(prodAdded => !detailSOData.map(dSO => dSO.M_Item_id_item).includes(String(prodAdded.id)));
      existingProducts = detailSOData.filter(prod => Object.keys(checkedItemFromDialog).includes(prod.M_Item_id_item));
    }

    const arrValue = filteredProducts.map(x => ({
      id: String(x.id),
      no: x.item_no,
      sku: x.sku,
      unit_no: x.unit_no,
      name: x.name,
      id_category_item: x.category_id,
      so_item_qty_stock: x.stock.akhir,
      item_stock_editable: x.stock.akhir,
      item_bahan_jadi: x.item_type,
      item_bahan_jadi_text: x.item_type_name,
      disabled: false,
      selisih: 0,
      catatan: '',
      satuan: x.unit_name,
      item_no: x.item_no,
      average_price: x.average_price,
      unit_conversion: x.units && x.units.length > 0 ? x.units[0].conversion : 1,
      variants: x.variants,
      ...(isPrimeAccount && { is_serial_number: x.is_serial_number }),
      ...(isPrimeAccount && { is_batch_number: x.is_batch_number }),
      ...(isPrimeAccount && x.is_serial_number && {
        serial_number: {
          serial_no: [],
        },
      }),
      ...(isPrimeAccount && x.is_batch_number && {
        batch_number: {
          is_reserved: false,
          batch_detail: [],
          use_expired: x.has_expired_date,
        },
      }),
    }));

    if (arrValue.length > 300) {
      addToast({
        title: t('mainPage.toast.failedPickProduct', 'Gagal pilih produk'),
        description: t('mainPage.toast.pickProduct.desc', 'Maksimal 300 produk'),
        variant: 'failed',
        dismissAfter: 2000,
      });
      callback();
    }

    const formattedDetailSO = reformatDefaultSOProducts(arrValue, isPrimeAccount, statusSO);
    const result = [...existingProducts, ...formattedDetailSO];
    setValItems('data', result);
    setDetailSO(result);
    setNewSOData(result);
  };

  const getDetailSOList = async (payload, transactionStatus) => {
    // setTableFetching with true value are for memoized table on stock opname form
    setTableFetching(true);

    let detailSOList = [];
    try {
      const res = await inventoryApi.getStockOpnameDetailList(payload);
      if (!res.status) throw new Error(res.msg);
      const { data: stockOpnameDetailList } = res;
      if (stockOpnameDetailList && stockOpnameDetailList.length > 0) {
        detailSOList = stockOpnameDetailList.map(item => ({
          ...item,
          id: item.id_stock_opname_detail,
          selisih:
            parseFloat(item.stock_opname_detail_actual || 0)
            - parseFloat(item.stock_opname_detail_registered || 0),
          averagePrice: Number(item.stock_opname_detail_actual || 0) - Number(item.stock_opname_detail_registered || 0) !== 0
            ? Number(item.item_price) * (Number(item.stock_opname_detail_actual || 0) - Number(item.stock_opname_detail_registered || 0))
            : Number(item.item_price),
          // set case_flag based on selisih from API data, default is 'plus'
          case_flag: (parseFloat(item.stock_opname_detail_actual || 0)
            - parseFloat(item.stock_opname_detail_registered || 0)) < 0 ? 'minus' : 'plus',
          ...(isPrimeAccount ? { is_serial_number: item.is_serial_number } : {}),
          ...(isPrimeAccount && item.is_serial_number ? {
            serial_number: {
              // default is_reserved is undefined
              is_reserved: !item.serial_number.serial_no ? undefined : item.serial_number.is_reserved,
              serial_no: item.serial_number.serial_no || [],
            },
          } : {}
          ),
          ...(isPrimeAccount && item.is_serial_number === 0 && item.batch_number.use_batch === true ? {
            batch_number: {
              ...item.batch_number,
              is_reserved: !item.batch_number.batch_detail ? undefined : item.batch_number.is_reserved,
              is_draft: true,
              batch_detail: !item.batch_number.batch_detail ? [] : item.batch_number.batch_detail.map(bnDetail => ({
                ...bnDetail,
                stock: Number(bnDetail.stock),
                stock_on_hand: Number(bnDetail.stock_on_hand),
              })),
            },
          } : {}
          ),
          isDraft: transactionStatus ? (Number(transactionStatus) === INVENTORIES_STATUS.DRAFT) : undefined,
        }));
      }
    } catch (error) {
      addToast({
        title: t('mainPage.toast.failTitle', 'Gagal!'),
        description: catchError(error),
        variant: 'failed',
        dismissAfter: 2000,
      });
    }
    return detailSOList;
  };

  const getDetailSO = async () => {
    showProgress();
    try {
      const payload = {
        so_id: params.id,
      };
      const res = await inventoryApi.getStockOpnameDetail(payload);
      if (!res.status) throw new Error(res.error);
      const { data } = res;
      if (data.status === '2' || (data.status !== '2' && !data.is_import_opname)) {
        const { M_Cabang_id_cabang: id_outlet } = data;
        const savedSortConfig = JSON.parse(getInfoStorage(SORT_KEY));
        const formatedDetailSO = await getDetailSOList(
          {
            ...payload,
            id_outlet,
            ...(savedSortConfig && {
              sort: savedSortConfig.desc ? 'desc' : 'asc',
              sort_by: savedSortConfig.id === 'M_Item_SKU' ? 'sku' : 'name',
            }),
          }, data.status,
        );
        setValItems('data', formatedDetailSO);
        setDetailSO(formatedDetailSO);
        setNewSOData(formatedDetailSO);
        // set tableFetching to false, so table shown
        setTableFetching(false);
      }
      getCurrentCabang(data);
      setFormData(data);
      setStatusSO(data.status);
      setStatusSOMessage(data.so_message);
      setValue('tanggal', data.tanggal);
    } catch (error) {
      addToast({
        title: t('mainPage.toast.failTitle', 'Gagal!'),
        description: catchError(error),
        variant: 'failed',
        dismissAfter: 2000,
      });
      // set table to false, when had error on get detail SO
      setTableFetching(false);
    } finally {
      hideProgress();
    }
  };

  const handleSNBNDialog = (index, typeDialog) => {
    const {
      M_Item_name: name,
      is_serial_number: isSN,
      batch_number: { use_batch: isBN },
      batch_number: registeredStockBN,
      M_Item_id_item: itemId,
      ...productRow
    } = getValItems(`data.${index}`);
    const idOutlet = Number(getValues('outlet.value'));

    const {
      selisih: receivedQty,
      stock_opname_detail_actual: stockOpnameDetailActual,
      serial_number: registeredStockSNActual,
    } = getValItems(`data.${index}`);

    let isNegativeCase = false;
    if (isSN) {
      isNegativeCase = receivedQty < 0;
      setSnbnFlag('SN');
    } else if (isBN) {
      isNegativeCase = receivedQty < 0;
      setSnbnFlag('BN');
    }
    const negativeCaseType = isNegativeCase ? 'minus' : 'plus';
    setCaseType(negativeCaseType);

    /**
     * formListSelisih are original selisih from selected row data by index,
     * use for compare original selisih with actual updated selisih,
     * consumed by compareChangedDetected to set flagChangeDetect, so if
     * SN Dialog has submitted yet, then pick selisih from original selisih,
     * otherwise, if SN Dialog has submitted, then compare value of caseType
     * with actutal negativeCaseType value (both are 'minus' or 'plus')
     */

    const {
      selisih: formListSelisih,
    } = getValItems(`data.${index}`);
    // flagChangeDetect are for detect selisih has changed or not
    const flagChangeDetect = dialogSubmitFlag ? negativeCaseType === caseType : compareChangedDetected([formListSelisih, receivedQty]);

    const parseProductRow = {
      // note: incl. case_flag
      ...productRow,
      name,
      idOutlet,
      itemId,
      receivedQty: isSN ? Math.abs(receivedQty) : receivedQty,
      // If registered stock not 0 & selisih minus or plus
      isNegativeCase,
      transactionNo: getValues('nomorSo'),
      negativeCaseType, // handle for default params in SN Dialog('plus')
      case_flag: negativeCaseType, // for comparison with default params in SN Dialog('plus')
      transactionType: 3, // 3 is transaction type for stok opname
      stockPage: 'opname',
      // stockOpnameDetailActual are actual value on changes of actual stock column, not only
      // from original detail stock opname, its for maxCount of Dialog Serial Number,
      stock_opname_detail_actual: stockOpnameDetailActual,
      ...(isSN && {
        serial_number: {
          ...registeredStockSNActual,
          // flagChangeDetect are comparison between negativeCaseType === caseType value and
          // compareChangedDetected([formListSelisih, receivedQty]) value, so if true (had'not
          // changed from negative to positif or vice versa, then populate registeredStockSN.serial_no,
          // otherwise reset value to empty array
          serial_no: flagChangeDetect ? registeredStockSNActual.serial_no.map(sn => (lodash.isObject(sn) ? sn.label : sn)) : [],
        },
      }),
      ...(isBN && {
        batch_number: {
          use_batch: registeredStockBN.use_batch,
          is_reserved: registeredStockBN.is_reserved,
          use_expired: registeredStockBN.use_expired,
          data: registeredStockBN.batch_detail && registeredStockBN.batch_detail.map(bn => ({
            batch_no: bn.batch_no,
            stock: bn.stock,
            stock_on_hand: bn.stock_on_hand,
            expired_date: bn.expired_at,
            is_new_draft_opname: bn.is_new_draft_opname,
          })),
        },
      }),
    };
    // set parseProductRow
    return snbnDialogRef.current.handleShowDialog(parseProductRow, typeDialog);
  };


  const fetchCheckHideAveragePrice = async () => {
    try {
      const res = await inventoryApi.checkHideAveragePrice();
      if (res.data) {
        setIsShowAveragePrice(res.data.is_show);
      }
    } catch (e) {
      console.error(e);
    }
  };

  const handleSaveSNBNData = (idProduct, dataSNBN) => {
    const cloneData = getValItems('data');

    let restructureDataSNBN = {
      ...dataSNBN,
    };
    if (snbnFlag === 'SN') {
      // note: restructure serial_number data(is_reserved & serial_no, for take off case_flag value)
      // populate other dataSNBN, except case_flag, is_reserved & serial_no
      const {
        serial_number: { case_flag, is_reserved, serial_no },
        ...populatedDataSNBN
      } = dataSNBN;
      // restructure serial_number value
      const restructureSerialNumber = { is_reserved, serial_no };

      // set dialogSubmitFlag to true indicates dialog submit had submitted
      setDialogSubmitFlag(true);
      // Setter Type Of Case, for resetter SN if happen reverse changes (plus to minus or otherwise)
      setCaseType(case_flag);

      // restructure dataSNBN, rewrite case_flag value
      restructureDataSNBN = { ...populatedDataSNBN, serial_number: restructureSerialNumber, case_flag };
    } else if (snbnFlag === 'BN') {
      const {
        batch_number: {
          case_flag,
          is_reserved,
          use_batch,
          use_expired,
          batch_detail,
        },
      } = dataSNBN;

      // restructure serial_number value
      const restructureBatchNumber = {
        use_batch: Boolean(use_batch),
        is_reserved,
        use_expired,
        batch_detail: use_expired
          ? batch_detail.map(bn => ({
            ...bn,
            expired_at: moment(bn.expired_date).format('YYYY-MM-DD HH:mm:ss'),
          }))
          : batch_detail,
      };

      // restructure dataSNBN, rewrite case_flag value
      restructureDataSNBN = { batch_number: restructureBatchNumber, case_flag };
    }

    // find index from detailSOData based on idProduct params
    const index = cloneData.findIndex(itemProduct => itemProduct.id === idProduct);
    clearErrors(`snbnQtyInvalid.${index}`);

    // assign new structure dataSNBN to selected index of detailSOData objects
    Object.assign(cloneData[index], restructureDataSNBN);

    // set new cloneData Modified value to RHF * detailSOData state
    setValItems('data', cloneData);
    setDetailSO(cloneData);
    setNewSOData(cloneData);
  };

  const detailSODraftSetter = (isReserved, detailSOEntry) => new Promise((resolve, reject) => {
    const products = detailSOEntry.map((product) => {
      if (product.is_serial_number === 1 && ishasProperty(product, 'serial_number')) {
        product.isDraft = !isReserved;
      }

      if (product.is_batch_number === 1 && ishasProperty(product, 'batch_number')) {
        product.isDraft = !isReserved;
      }

      return product;
    });
    setDetailSO(products);
    setNewSOData(products);
    resolve(products);
  });

  const submitChecker = async (reservedFlag) => {
    if (isPrimeAccount) {
      // check if has draft tag
      const snbnEntryChecker = await detailSODraftSetter(reservedFlag, detailSOData).then(val => itemSNBNHaventEntry(val));
      const snbnEntryQtyChecker = await detailSODraftSetter(reservedFlag, detailSOData).then(val => itemSNBNQtyValidation(val));
      if (reservedFlag) {
        if (draftTagChecker(detailSOData)) return setSnbnTagDraftPopup(true);
        if (snbnEntryChecker.invalid && snbnEntryChecker.item.length > 0) {
          snbnEntryChecker.item.map((err, index) => {
            setError(`snbnQtyInvalid.${index}`, { type: 'snbnQtyInvalid', message: err });
          });
          return addToast({
            title: 'Gagal!',
            description: 'Harap isi Serial/Batch Number sesuai jumlah',
            variant: 'failed',
          });
        }

        if (snbnEntryQtyChecker.invalid && snbnEntryQtyChecker.item.length > 0) {
          snbnEntryQtyChecker.item.map((err, index) => {
            setError(`snbnQtyInvalid.${index}`, { type: 'snbnQtyInvalid', message: err });
          });
          return addToast({
            title: 'Gagal!',
            description: 'Harap isi Serial/Batch Number sesuai jumlah',
            variant: 'failed',
          });
        }
      }
    }

    if (reservedFlag && detailSOData.length && detailSOData.some(item => item.stock_opname_detail_actual < 0)) {
      detailSOData.filter(item => item.stock_opname_detail_actual < 0).forEach((item, index) => {
        setError(`data.${index}.stock_opname_detail_actual`, {
          type: 'stockActualNegative',
          message: `Stok Aktual ${item.M_Item_name} tidak dapat negatif`,
        });
      });

      return addToast({
        title: 'Gagal!',
        description: 'Terdapat Stok Aktual negatif, mohon diperiksa kembali',
        variant: 'failed',
      });
    }

    return onCompleteSave(reservedFlag);
  };

  const stringTitle = useCallback(() => {
    if (type === 'create') {
      return t('createEditModal.addDialogTitle', 'Tambah Stok Opname');
    }
    if (formData.status) {
      if (parseInt(formData.status, 10) === 1) {
        return 'Detail Stok Opname';
      }
      return t('createEditModal.updateDialogTitle', 'Ubah Stok Opname');
    }
    return t('mainPage.title', 'Ubah Stok Opname');
  }, [type, formData.status]);

  useEffect(async () => {
    if (params.type === 'detail') {
      getDetailSO();
    }
    const outletList = await fetchOutletData();
    setListOutlet(outletList.map(i => ({ id: i.id_cabang, name: i.cabang_name })));
    hideProgress();
  }, [JSON.stringify(params)]);

  useEffect(() => {
    if (params.type !== 'detail' && listOutlet && listOutlet.length > 0) {
      if (filterBranch) {
        const initOutlet = listOutlet.find(item => Number(item.id) === Number(filterBranch));
        const { id: value = '', name } = initOutlet;
        setValue('outlet', { value, name });
        selectedCabangInfoRef.current = initOutlet;
        setSelectedCabangInfo(initOutlet);
      }
    }
  }, [JSON.stringify({ params, listOutlet, filterBranch })]);

  useEffect(() => {
    fetchCheckHideAveragePrice();
    checkEligibility(schemes.backdateStockOpname);
  }, []);

  // render
  return (
    <React.Fragment>
      <StockOpnameContext.Provider
        value={{
          caseType,
          dialogSubmitFlag,
          detailSOData,
          props,
          router,
          cabangInfo: selectedCabangInfo,
          formData,
          listOutlet,
          onEksporFile: dialogEkspor,
          buildPayload: buildSavePayload,
          onApplyProduk: applyProduct,
          onRemoveBarang,
          onChangeStokActual,
          onPrint: showPrintPopup,
          useFormSODetails: {
            registerForm: register,
            handleSubmit,
            getFormValues: getValues,
            setFormValue: setValue,
            watch,
            rowErrors: errors,
            clearErrors,
            setError,
          },
          useFormDetailsBarang: {
            controllerItems,
            registerItems,
            getValItems,
            setValItems,
            watchItems,
          },
          translation: { t, i18n, ready },
          onShowSerialBatchNumberDlg: handleSNBNDialog,
          isPrimeAccount,
          checkedItems,
          setCheckedItems,
          tableFetching,
          newSOData,
          isShowAveragePrice,
          isDetail,
          isBackdateWhitelisted,
        }}
      >
        <div id="a-dialog" />
        <PageDialog
          open
          modal
          size="full"
          onOpenChange={handleCloseDetailDialog}
          css={{ zIndex: '$modal' }}
          isMobile={isMobile}
        >
          <PageDialogTitle>
            {stringTitle()}
          </PageDialogTitle>
          <PageDialogContent
            css={{
              '@md': {
                display: 'flex',
                flexDirection: 'column',
                gap: 32,
                padding: 40,
                ...(bannerStatus ? {
                  '& > div': {
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '$cozy',
                  },
                } : {}),
              },
            }}
          >
            {/* Banner Import Status */}
            {/* On Process Import Stock Opname Banner */}
            {
              statusSO === '3' && bannerStatus && (
                <StockOpnameImportBannerStatus
                  variant="pending"
                  description={statusSOMessage || t('mainPage.banner.pending')}
                  setCloseBanner={() => setBannerStatus(false)}
                  css={{ '@md': { width: 982, margin: '0 auto' } }}
                />
              )
            }

            {/* Fail Import Stock Opname Banner */}
            {
              statusSO === '8' && bannerStatus && (
                <StockOpnameImportBannerStatus
                  variant="failed"
                  title={t('mainPage.toast.failTitle', 'Gagal!')}
                  description={t('mainPage.banner.failed')}
                  css={{ '@md': { width: 982, margin: '0 auto' } }}
                />
              )
            }

            {/* Half Suceeded Import Stock Opname Banner */}
            {
              statusSO === '5' && bannerStatus && (
                <StockOpnameImportBannerStatus
                  variant="info"
                  title={t('mainPage.banner.successPartialTitle', 'Berhasil Sebagian')}
                  description={statusSOMessage || t('mainPage.banner.successPartial')}
                  css={{ '@md': { width: 982, margin: '0 auto' } }}
                />
              )
            }
            {type === 'detail' && formData.id_stock_opname && (
              <StockStatusPaper status={formData.status} date={formData.updatedate} by={formData.updateby_name} />
            )}
            <Paper css={{
              '@sm': { padding: 16 },
              '@md': { padding: 20, width: 982, margin: '0 auto' },
            }}
            >
              <FormStockOpname />
            </Paper>
          </PageDialogContent>
          <PageDialogFooter
            css={{
              display: 'flex',
              gap: '$compact',
              '@sm': {
                padding: 16,
                justifyContent: 'center',
              },
              '@md': {
                padding: '12px 24px 12px 12px',
              },
            }}
          >
            {type === 'detail' && statusSO !== '2' ? (
              <Flex
                justify="end"
                css={{
                  '@md': {
                    width: 982,
                    margin: '0 auto',
                  },
                }}
              >
                <Button
                  onClick={onClose}
                  buttonType="ghost"
                  aria-label="back"
                  size="md"
                  css={{
                    width: '100%',
                    '@md': {
                      width: 'unset',
                    },
                  }}
                  disabled={statusSO === ''}
                >
                  {t('createEditModal.footerClose', 'Tutup')}
                </Button>
              </Flex>
            ) : (
              <Flex
                justify="between"
                align="center"
                css={{
                  width: 982,
                  margin: '0 auto',
                }}
              >
                {isMobile ? (
                  <IconButton onClick={() => toggleModalOption(true)}>
                    <EllipsisHorizontalOutline color={foundations.colors.gray400} />
                  </IconButton>
                ) : (
                  <Flex align="start" css={{ gap: 16 }}>
                    {menuPrivilege.isCanDelete && stringTitle() !== t('createEditModal.addDialogTitle') && (
                      <Button
                        onClick={onDeleteAction}
                        css={{
                          width: '40px !important',
                          minWidth: 'unset !important',
                          padding: 8,
                        }}
                        buttonType="negative-secondary"
                      >
                        <TrashOutline
                          size="24"
                          color={foundations.colors.btnNegative}
                        />
                      </Button>
                    )}
                    <Button
                      type="button"
                      onClick={() => submitChecker(false)}
                      buttonType="secondary"
                    >
                      {t('createEditModal.footerSaveDraft', 'Simpan Draf')}
                    </Button>
                  </Flex>
                )}
                <Flex
                  css={{
                    gap: 16,
                    flex: 1,
                    '@md': {
                      justifyContent: 'flex-end',
                      flex: 'unset',
                    },
                  }}
                >
                  <Button
                    onClick={onBatalAction}
                    buttonType="ghost"
                    css={{
                      width: '100%',
                      '@md': {
                        width: 'unset',
                      },
                    }}
                  >
                    {t('createEditModal.footerCancel', 'Batal')}
                  </Button>
                  <Button
                    type="button"
                    onClick={() => submitChecker(true)}
                    buttonType="primary"
                    css={{
                      width: '100%',
                      '@md': {
                        width: 'unset',
                      },
                    }}
                  >
                    {t('createEditModal.footerSave', 'Simpan')}
                  </Button>
                </Flex>
              </Flex>
            )}
          </PageDialogFooter>
        </PageDialog>
        <ModalDialog
          css={{ zIndex: '$popover' }}
          isMobile={isMobile}
          size="md"
          open={modalCetak}
          onOpenChange={() => setModalCetak(prev => !prev)}
          modal
          id="modal-print"
        >
          <ModalDialogTitle>{t('createEditModal.modal.print.title', 'Cetak')}</ModalDialogTitle>
          <ModalDialogContent>
            <div className="row mt-sm mb-xs">
              <div className="col-sm-10">{t('createEditModal.modal.print.showSystemStock', 'Tampilkan stok sistem')}</div>
              <div className="col-sm-2">
                <div style={{ display: 'inline-block', float: 'right' }}>
                  <InputCheckbox
                    id="isTampilStokSistem"
                    checked={isSystemStock}
                    changeEvent={e => setisSystemStock(e)}
                  />
                </div>
              </div>
            </div>
          </ModalDialogContent>
          <ModalDialogFooter>
            <Flex css={{ width: '$full', gap: 8 }} justify="end">
              <Button
                onClick={() => setModalCetak(prev => !prev)}
                buttonType="ghost"
              >
                {t('createEditModal.footerCancel', 'Batal')}
              </Button>
              <Button onClick={doPrintHandler}>
                {t('createEditModal.printBtn', 'Cetak')}
              </Button>
            </Flex>
          </ModalDialogFooter>
        </ModalDialog>
      </StockOpnameContext.Provider>

      <ModalDialog isMobile modal open={modalOption} onOpenChange={toggleModalOption}>
        <ModalDialogTitle>{t('createEditModal.modal.advanceOption.title', 'Opsi Lanjutan')}</ModalDialogTitle>
        <ModalDialogContent css={{ padding: '20px 16px' }}>
          <Box css={{ display: 'flex', flexDirection: 'column', gap: 20 }}>
            <Box
              type="button"
              role="button"
              onClick={() => {
                toggleModalOption(false);
                onCompleteSave(false);
              }}
            >
              <Paragraph paragraph="shortContentRegular" color="primary">
                {t('createEditModal.modal.advanceOption.draftAction', 'Simpan Draf')}
              </Paragraph>
            </Box>
            <Separator />
            {menuPrivilege.isCanDelete && stringTitle() !== t('createEditModal.addDialogTitle') && (
              <Box
                type="button"
                role="button"
                onClick={() => {
                  toggleModalOption(false);
                  onDeleteAction();
                }}
              >
                <Paragraph paragraph="shortContentRegular" color="primary">
                  {t('createEditModal.modal.advanceOption.deleteAction', 'Hapus')}
                </Paragraph>
              </Box>
            )}
          </Box>
        </ModalDialogContent>
      </ModalDialog>
      <SerialBatchNumberDialog
        ref={snbnDialogRef}
        onSaveDataSNBN={(idProduct, dataSNBN) => handleSaveSNBNData(idProduct, dataSNBN)}
        isStockIn={false}
        translate={t}
      />
      {
        snbnTagDraftPopup ? (
          <InfoDialog
            css={{ width: 422 }}
            open={snbnTagDraftPopup}
            isMobile={!matchMediaChecker(MATCH_MEDIA_TYPE.MD)}
            title="Perhatian!"
            description="Terdapat produk dengan Serial/Batch Number yang berstatus Draf. Silakan perbarui status produk dengan klik tombol Ubah yang terletak di kolom Serial/Batch Number, lalu klik Simpan"
            onConfirm={() => setSnbnTagDraftPopup(false)}
          />
        ) : null
      }
    </React.Fragment>
  );
});

StockOpnameCreate.propTypes = {
  router: PropType.shape(),
  params: PropType.shape(),
  location: PropType.shape(),
  userWhitelist: PropType.shape(),
  actions: PropType.shape(),
  menuPrivilege: PropType.shape().isRequired,
  showProgress: func,
  hideProgress: func,
  idCabang: string,
  filterBranch: string,
};

StockOpnameCreate.defaultProps = {
  router: {},
  params: {},
  location: {},
  userWhitelist: {},
  actions: {},
  showProgress: () => { },
  hideProgress: () => { },
  idCabang: '',
  filterBranch: ''
};

const mapStateToProps = state => ({
  userWhitelist: state.popUpHppReducer,
  layout: state.layout,
  menuPrivilege: state.layouts.detailPrivilege,
});

const mapDispatchToProps = dispatch => ({
  actions: {
    outlet: bindActionCreators(outletActions, dispatch),
    product: bindActionCreators(productApi, dispatch),
  },
});

const WithRouter = (props) => {
  const location = useLocation();
  const params = useParams();

  return <StockOpnameCreate {...props} {...{ location, params }} />;
};

export default CoreHOC(
  connect(mapStateToProps, mapDispatchToProps)(WithRouter),
);

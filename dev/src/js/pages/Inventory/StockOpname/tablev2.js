/* eslint-disable react/prop-types */
/* eslint-disable camelcase */
import React, { useState } from 'react';
import moment from 'moment';
import { Controller } from 'react-hook-form';
import {
  RowExpanderColumn,
  RowSelectionColumn,
  DateColumn,
  PriceColumn,
  TagStatus,
  IconButton,
  InputGroup,
  InputText,
  InputNumber,
  InputTextArea,
  InputRightElement,
  RowMenuColumn,
  Box,
  ContextMenuTrigger,
  Button,
  Flex,
  Text,
} from '@majoo-ui/react';
import { foundations } from '@majoo-ui/core';
import {
  EyeOutline,
  TrashOutline,
} from '@majoo-ui/icons';
import { currency, ishasProperty, numSeparator } from '../../../utils/helper';

import { statusSOParser } from './V2/StockOpnameV2Utils';
import { ConditionalWrapper } from '../../../components/wrapper/ConditionalWrapper';
import BigProductName from '../../../components/retina/BigProductName';
import ProductVariants from '../StockEntryV2/DialogProductList/ProductVariants';
import { sortTable } from '../../../utils/table.util';

const CurrencyColumn = props => PriceColumn(props).split(',').slice(0, 1).join('');

export const TagsFormatter = ({ value, t }) => {
  const { labelStatus, tagStatus } = statusSOParser(t)[value];
  return (
    <TagStatus type={tagStatus} css={{ maxWidth: 'unset' }}>{labelStatus}</TagStatus>
  );
};

const ContextTrigger = ({ children, original, onRowClick }) => (
  <ConditionalWrapper
    condition={original.status === '2'}
    wrapper={child => (
      <ContextMenuTrigger triggerSide="left" id="context-menu-component">
        {child}
      </ContextMenuTrigger>
    )}
  >
    <Box id="contextClick" onClick={() => onRowClick(original)}>{children}</Box>
  </ConditionalWrapper>
);

export const stockOpnameTableMeta = (router, deleteAction, onRowClick, translation, isCanDelete) => [
  {
    Header: translation('mainPage.columnHeader.date'),
    accessor: 'tanggal',
    colWidth: 223,
    Cell: ({ cell: { value }, ...props }) => (
      <ContextTrigger original={props.row.original} onRowClick={x => onRowClick(x)}>
        {`${DateColumn(props).props.children}, ${moment(value).format('HH:mm')}`}
      </ContextTrigger>
    ),
  },
  {
    Header: translation('mainPage.columnHeader.number'),
    accessor: 'nomor',
    isMobileHeader: true,
    Cell: ({ value, row: { original } }) => <ContextTrigger onRowClick={x => onRowClick(x)} original={original}>{value}</ContextTrigger>,
  },
  {
    Header: translation('mainPage.columnHeader.outlet'),
    accessor: 'nama_cabang',
    Cell: ({ value, row: { original } }) => <ContextTrigger onRowClick={x => onRowClick(x)} original={original}>{value}</ContextTrigger>,
  },
  {
    Header: translation('translation:label.createdBy'),
    accessor: 'createby_name',
    Cell: ({ value }) => (value || '-'),
  },
  {
    Header: translation('mainPage.columnHeader.status'),
    accessor: 'status',
    Cell: ({ value, row: { original } }) => (
      <ContextTrigger onRowClick={x => onRowClick(x)} original={original}>
        <TagsFormatter t={translation} value={value} />
      </ContextTrigger>
    ),
  },
  {
    Header: () => null,
    id: 'view',
    isAction: true,
    Cell: (props) => {
      const { row } = props;
      if (row.original.status !== '2') {
        return (
          <IconButton
            onClick={() => {
              router.push(`/inventory/kelola-stok/stock-opname/detail/${row.original.id_stock_opname}`, {
                state: {
                  statusSO: row.original.status,
                },
              });
            }}
          >
            <EyeOutline />
          </IconButton>
        );
      }
      return RowMenuColumn(
        isCanDelete ? [
          {
            title: translation('mainPage.columnHeader.action.detail'),
            onClick: (prop) => {
              router.push(`/inventory/kelola-stok/stock-opname/detail/${prop.row.original.id_stock_opname}`, {
                state: {
                  statusSO: row.original.status,
                },
              });
            },
          },
          {
            title: translation('mainPage.columnHeader.action.delete'),
            onClick: (prop) => {
              deleteAction(prop.row.original);
            },
          },
        ] : [{
          title: translation('mainPage.columnHeader.action.detail'),
          onClick: (prop) => {
            router.push(`/inventory/kelola-stok/stock-opname/detail/${prop.row.original.id_stock_opname}`, {
              state: {
                statusSO: row.original.status,
              },
            });
          },
        }],
      ).Cell(props);
    },
  },
];

export const tableProdukMeta = t => [
  RowExpanderColumn,
  RowSelectionColumn,
  {
    Header: t('addProductModal.tableColumn.sku', 'SKU'),
    accessor: 'sku',
    colWidth: 124,
  },
  {
    Header: t('addProductModal.tableColumn.product', 'PRODUK'),
    accessor: 'name',
    colWidth: 124,
    Cell: ({ cell: { value }, ...props }) => {
      const { variants } = props.row.original;
      return (
        <Box>
          <BigProductName
            name={value}
            css={{ fontWeight: 400, wordBreak: 'break-word' }}
            color="primary"
            withTooltip
            maxLength={50}
          />
          {variants && variants.length > 0 && <ProductVariants data={variants} />}
        </Box>
      );
    },
    isMobileHeader: true,
  },
  {
    Header: t('addProductModal.tableColumn.category', 'KATEGORI'),
    accessor: 'category_name',
  },
  {
    Header: t('addProductModal.tableColumn.type', 'JENIS'),
    accessor: 'item_type',
    Cell: ({ cell: { row: { original: { item_type_name } } } }) => <div>{item_type_name}</div>,
  },
  {
    Header: t('addProductModal.tableColumn.stock', 'STOK'),
    accessor: 'stock.akhir',
    unsortable: true,
    Cell: ({ value }) => (value < 0 ? `(${numSeparator(value).replace('-', '')})` : numSeparator(value)),
  },
  {
    Header: t('addProductModal.tableColumn.purchasePrice', 'HARGA BELI'),
    accessor: 'purchase_price',
    unsortable: true,
    Cell: CurrencyColumn,
  },
  {
    Header: t('addProductModal.tableColumn.sellingPrice', 'HARGA JUAL'),
    accessor: 'selling_price',
    unsortable: true,
    Cell: CurrencyColumn,
  },
  {
    Header: t('addProductModal.tableColumn.capitalCost', 'Harga Modal'),
    accessor: 'average_price',
    unsortable: true,
    isSubRow: true,
    Cell: ({ value }) => currency({ value, decimal: String(value).includes('.') }),
  },
  {
    Header: t('addProductModal.tableColumn.unit', 'Satuan'),
    accessor: 'unit_name',
    unsortable: true,
    isSubRow: true,
  },
];

export const tableDetails = (
  onRemove,
  form,
  register,
  setValue,
  getValue,
  control,
  onChange,
  onShowSerialBatchNumberDlg,
  rowErrors,
  clearErrors,
  isPrimeAccount,
  setError,
  trans,
  isDetail,
) => [
    {
      Header: trans('createEditModal.editableTableColumn.sku', 'SKU'),
      accessor: 'M_Item_SKU',
      width: 106,
      sortable: true,
      sortType: sortTable,
    },
    {
      Header: trans('createEditModal.editableTableColumn.goods', 'BARANG'),
      accessor: 'M_Item_name',
      width: 152,
      isMobileHeader: true,
      sortable: true,
      sortType: sortTable,
      Cell: ({ cell: { value }, row: { original } }) => {
        const {
          variants,
          is_serial_number: isSN,
          serial_number = {},
          batch_number = {},
        } = original;
        let getStatusSNorBN = null;
        if (isPrimeAccount && isSN && ishasProperty(serial_number, 'is_reserved')) {
          getStatusSNorBN = serial_number.is_reserved === false ? true : undefined;
        } else if (batch_number.use_batch && ishasProperty(batch_number, 'is_reserved')) {
          getStatusSNorBN = batch_number.is_reserved === false ? true : undefined;
        }

        return (
          <Box>
            <Flex gap={2}>
              <BigProductName
                name={value}
                css={{ fontWeight: 400, wordBreak: 'break-word' }}
                color="primary"
                type="detail"
                maxLength={80}
              />
              {getStatusSNorBN && <TagStatus>Draft</TagStatus>}
            </Flex>
            {variants && variants.length > 0 && <ProductVariants data={variants} />}
          </Box>
        );
      },
    },
    {
      Header: trans('createEditModal.editableTableColumn.systemStock', 'STOK SISTEM'),
      accessor: 'stock_opname_detail_registered',
      width: 106,
      Cell: ({ cell: { value }, ...props }) => {
        const { unit_name } = props.row.original;
        return (
          <InputGroup
            size="sm"
            css={{
              padding: '6px 0',
              backgroundColor: '$bgInactive',
            }}
            readOnly
          >
            <InputText readOnly size="sm" css={{ minWidth: 50 }} defaultValue={numSeparator(value) || 0} />
            <InputRightElement css={{ color: '$iconSecondary' }}>
              {unit_name}
            </InputRightElement>
          </InputGroup>
        );
      },
    },
    {
      Header: trans('createEditModal.editableTableColumn.actualStock', 'STOK AKTUAL'),
      accessor: 'stock_opname_detail_actual',
      width: 106,
      Cell: ({ row: { original } }) => {
        const isDisabled = 'status' in form && form.status !== '2';
        const index = getValue('data').findIndex(item => item.id === original.id);

        return (
          <Controller
            control={control}
            name={`data.${index}.stock_opname_detail_actual`}
            render={({ field: { onChange: onChangeActualValue, value }, fieldState: { error } }) => (
              <Flex direction="column" gap={2}>
                <InputNumber
                  size="sm"
                  value={value}
                  onValueChange={({ floatValue }, e) => {
                    if (e.event) {
                      const updatedFloatValue = floatValue !== undefined ? floatValue : 0;
                      const difference = updatedFloatValue - Number(original.stock_opname_detail_registered || 0);

                      onChangeActualValue(updatedFloatValue);
                      setValue(`data.${index}.selisih`, difference);
                      setValue(`data.${index}.averagePrice`, difference !== 0 ? Number(original.item_price) * difference : Number(original.item_price));
                    }
                  }}
                  decimalScale={2}
                  readOnly={isDisabled}
                  allowNegative={Number(original.stock_opname_detail_registered) < 0}
                  isAllowed={({ floatValue }) => (Number(original.stock_opname_detail_registered) >= 0
                    ? floatValue >= 0
                    : floatValue
                  )
                    || floatValue === undefined
                  }
                />
                <Text css={{ color: 'red' }}>{error?.message}</Text>
              </Flex>
            )}
          />
        );
      },
    },
    {
      Header: trans('createEditModal.editableTableColumn.difference', 'SELISIH'),
      accessor: 'selisih',
      width: 70,
      Cell: ({ row: { original } }) => {
        const index = getValue('data').findIndex(item => item.id === original.id);

        return (
          <Controller
            control={control}
            name={`data.${index}.selisih`}
            render={({ field: { value } }) => numSeparator(value)}
          />
        );
      },
    },
    {
      Header: trans('createEditModal.editableTableColumn.modalPrice', 'HARGA MODAL'),
      accessor: 'averagePrice',
      Cell: ({ row: { original } }) => {
        const index = getValue('data').findIndex(item => item.id === original.id);

        return (
          <Controller
            control={control}
            name={`data.${index}.averagePrice`}
            render={({ field: { value } }) => currency({ value, decimal: true })}
          />
        );
      },
    },
    ...(isPrimeAccount ? [{
      Header: 'SERIAL NUMBER/\nBATCH NUMBER',
      isRequired: true,
      width: 140,
      Cell: ({ row: { original } }) => {
        const index = getValue('data').findIndex(item => item.id === original.id);

        let isBN = 0,
          isSN = 0,
          selisih = 0,
          serial_number = null,
          batch_number = null,
          isDraft = false,
          id_stock_opname_detail = 0;
        if (index >= 0 && getValue('data')[index]) {
          ({
            batch_number: {
              use_batch: isBN,
            },
            is_serial_number: isSN = 0,
            selisih,
            serial_number,
            batch_number,
            isDraft,
            id_stock_opname_detail,
          } = getValue('data')[index]);
        }
        const isDisabled = 'status' in form && form.status !== '2';
        const snbnType = isSN ? 'SN' : 'BN';
        const intVal = Number(selisih) && Number(selisih) !== 0;
        let isInvalidQty = false;

        const batchNumberDataOrBatchDetail = batch_number ? batch_number.data || batch_number.batch_detail : [];

        // check invalid by check serial / batch number entry
        if (
          rowErrors && rowErrors.snbnQtyInvalid && rowErrors.snbnQtyInvalid.length > 0
          && rowErrors.snbnQtyInvalid.some(sbqi => Number(sbqi.message) === Number(id_stock_opname_detail))
        ) {
          isInvalidQty = true;
        } else {
          isInvalidQty = false;
        }

        if ((isSN && serial_number && serial_number.serial_no && Math.abs(selisih) === serial_number.serial_no.length)
          || (
            isBN && batch_number && batch_number.batch_detail && Math.abs(selisih) === (batch_number.batch_detail.reduce((prev, curr) => prev + curr.stock_on_hand, 0) - batch_number.batch_detail.reduce((prev, curr) => prev + curr.stock, 0))
          )
        ) {
          isInvalidQty = false;
        }

        if ((isSN && serial_number && serial_number.is_reserved === undefined && isDraft) || (isBN && batch_number && batch_number.is_reserved === undefined && isDraft)) {
          isInvalidQty = false;
        }

        if (isBN && isDraft === false && !batchNumberDataOrBatchDetail.every(bn => (ishasProperty(bn, 'stock_on_hand') ? bn.stock_on_hand > 0 : true))) {
          isInvalidQty = true;
        }

        return (
          <Button
            // buttonType={isInvalidQty && isReserved ? 'negative' : 'secondary'}
            buttonType={isInvalidQty ? 'negative-secondary' : 'secondary'}
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              onShowSerialBatchNumberDlg(index, snbnType);
            }}
            disabled={isDisabled || !intVal || (!isSN && !isBN)}
            css={{
              flex: 1,
            }}
          >
            {trans('translation:label.edit', 'Ubah')}
          </Button>
        );
      },
    }] : []),
    {
      Header: trans('createEditModal.editableTableColumn.note', 'Catatan'),
      accessor: 'stock_opname_note',
      isSubRow: true,
      Cell: ({ value, row: { original } }) => {
        const isDisabled = 'status' in form && form.status !== '2';
        const [noteLength, setNoteLength] = useState(0);
        const index = getValue('data').findIndex(item => item.id === original.id);

        return (
          <Controller
            control={control}
            name={`data.${index}.stock_opname_note`}
            render={({ field: { onChange: onChangeCatatan } }) => (
              <Flex
                direction="column"
                gap={3}
              >
                <InputTextArea
                  readOnly={isDisabled}
                  placeholder={isDetail ? '' : trans('createEditModal.placeholderNote', 'Contoh: Barang Hilang')}
                  defaultValue={value}
                  onChange={(event) => {
                    onChangeCatatan(event.target.value);
                    setNoteLength(event.target.value.length);
                  }}
                  maxLength={50}
                />
                <Flex justify="end">
                  <Text css={{ fontSize: 12 }}>
                    {noteLength}
                    /50
                  </Text>
                </Flex>
              </Flex>
            )}
          />
        );
      },
    },
    ...(!('status' in form && form.status !== '2') ? [{
      Header: '',
      id: 'delete',
      width: 42,
      Cell: (props) => {
        const { row } = props;
        return (
          <TrashOutline
            onClick={form.status !== '1' ? () => onRemove(row.original) : () => { }}
            color={foundations.colors.iconSecondary}
          />
        );
      },
    }] : []),
  ];
